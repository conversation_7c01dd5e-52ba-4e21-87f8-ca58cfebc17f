# 接口文档

## 登录相关的接口：
swagger地址： http://192.168.0.205/prod-api/swagger-ui/index.html#/%E7%99%BB%E5%BD%95%E9%AA%8C%E8%AF%81/loginUsingPOST


请求地址：http://192.168.0.205:80/prod-api/login
请求参数:
{
  "username": "bydq_admin",
  "password": "Aa123456",
  "uuid": "",
  "sourceType": 1
}
返回结果：
{
  "msg": "操作成功",
  "code": 200,
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjExYWM5Njk2LWYzNDctNGM3MC05ZmMyLWYyMDc1ZTM3OWM1NyJ9.vMQFFt3wdJvU1mfHus9KCeXL64SlKvyQOJOObuoAAZNc0hiqoc5be1INZSfOPvMWR5pe4bMiZYwMjg-p--_ooA"
}
把token加上「Bearer 」保存到cookie中，key为 Admin-Token ，value为返回的token值。

登录成功后，调用其他接口时，需要在事件表头上，加上 Authorization: 内容从Admin-Token中获取，即 config.js中getTokenFromCookies方法。

## 事件接口
swagger地址：http://192.168.0.205/prod-api/swagger-ui/index.html#/%E8%AE%BE%E5%A4%87%E5%91%8A%E8%AD%A6alertLog%E6%A8%A1%E5%9D%97/listUsingGET_1

### 报警列表:
请求地址： http://192.168.0.205:80/prod-api/iot/alertLog/list?alertName=%E6%8A%A5%E8%AD%A6%E8%AE%BE%E5%A4%87
反馈结果：
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 25,
  "rows": [
    {
      "alertLogId": 135914,
      "alertName": "报警设备",
      "alertLevel": 1,
      "status": 2,
      "serialNumber": "D19DWJ1674O77",
      "productId": 190,
      "detail": "{\"id\": \"HMI_30037_3\", \"name\": \"C相PWM板ROM参数故障\", \"value\": \"1\"}",
      "createBy": "bydq_admin",
      "createTime": "2025-07-23 09:41:59",
      "updateBy": "",
      "updateTime": null,
      "remark": null,
      "userId": 19,
      "deviceName": "报警设备",
      "deviceId": null,
      "sceneId": 145,
      "sceneName": null,
      "beginTime": null,
      "endTime": null,
      "deptUserId": null
    },
    {
      "alertLogId": 135913,
      "alertName": "报警设备",
      "alertLevel": 1,
      "status": 2,
      "serialNumber": "D19DWJ1674O77",
      "productId": 190,
      "detail": "{\"id\": \"HMI_30036_3\", \"name\": \"B相PWM板ROM参数故障\", \"value\": \"1\"}",
      "createBy": "bydq_admin",
      "createTime": "2025-07-23 09:41:11",
      "updateBy": "",
      "updateTime": null,
      "remark": null,
      "userId": 19,
      "deviceName": "报警设备",
      "deviceId": null,
      "sceneId": 134,
      "sceneName": null,
      "beginTime": null,
      "endTime": null,
      "deptUserId": null
    }
}
```

### 故障设备
请求地址： http://192.168.0.205:80/prod-api/iot/alertLog/list?alertName=%E6%95%85%E9%9A%9C%E8%AE%BE%E5%A4%87
反馈结果
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 25,
  "rows": [
    {
      "alertLogId": 135914,
      "alertName": "故障设备",
      "alertLevel": 1,
      "status": 2,
      "serialNumber": "D19DWJ1674O77",
      "productId": 190,
      "detail": "{\"id\": \"HMI_30037_3\", \"name\": \"C相PWM板ROM参数故障\", \"value\": \"1\"}",
      "createBy": "bydq_admin",
      "createTime": "2025-07-23 09:41:59",
      "updateBy": "",
      "updateTime": null,
      "remark": null,
      "userId": 19,
      "deviceName": "故障设备",
      "deviceId": null,
      "sceneId": 145,
      "sceneName": null,
      "beginTime": null,
      "endTime": null,
      "deptUserId": null
    },
    {
      "alertLogId": 135913,
      "alertName": "故障设备",
      "alertLevel": 1,
      "status": 2,
      "serialNumber": "D19DWJ1674O77",
      "productId": 190,
      "detail": "{\"id\": \"HMI_30036_3\", \"name\": \"B相PWM板ROM参数故障\", \"value\": \"1\"}",
      "createBy": "bydq_admin",
      "createTime": "2025-07-23 09:41:11",
      "updateBy": "",
      "updateTime": null,
      "remark": null,
      "userId": 19,
      "deviceName": "故障设备",
      "deviceId": null,
      "sceneId": 134,
      "sceneName": null,
      "beginTime": null,
      "endTime": null,
      "deptUserId": null
    }
}
```


### 定值:
http://192.168.0.205/prod-api/swagger-ui/index.html#/%E8%AE%BE%E5%A4%87%E6%9C%8D%E5%8A%A1%E4%B8%8B%E5%8F%91%E6%97%A5%E5%BF%97/listUsingGET_13
请求参数:
serialNumber(设备编号)

### 变化(待开发)

## 调试模块信息初始化接口：
swagger地址：http://192.168.0.205/prod-api/swagger-ui/index.html#/%E8%AE%BE%E5%A4%87%E7%AE%A1%E7%90%86/getRunningStatusInfoUsingGET

请求地址： http://192.168.0.205:80/prod-api/iot/device/runningStatus?deviceId=298
返回结果：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "deviceId": 298,
    "deviceName": "保护使能",
    "productId": 197,
    "productName": "调试参数",
    "deviceType": null,
    "tenantId": 19,
    "tenantName": "白云电器",
    "serialNumber": "D19EOEN59V1MJ",
    "firmwareVersion": 1,
    "status": 3,
    "isShadow": 0,
    "rssi": 0,
    "thingsModelValue": "[{\"id\": \"SVG_2201\", \"ts\": \"2025-09-05 14:52:05.424\", \"value\": \"16843009\", \"shadow\": \"16843009\"}, {\"id\": \"SVG_2016\", \"ts\": \"2025-09-05 14:52:09.85\", \"value\": \"17.0\", \"shadow\": \"17.0\"}, {\"id\": \"SVG_1706\", \"ts\": \"2025-09-05 14:52:25.942\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"2101_GUA1\", \"ts\": \"2025-09-05 14:53:10.797\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1303\", \"ts\": \"2025-09-05 14:52:29.224\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1509\", \"ts\": \"2025-09-05 14:52:25.932\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1010\", \"ts\": \"2025-09-05 14:52:29.193\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_SOCPLP\", \"ts\": \"2025-09-05 14:53:10.81\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2101_ABSSCFP\", \"ts\": \"2025-09-05 14:53:10.804\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1940\", \"ts\": \"2025-09-05 14:52:09.813\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"1914_MSM\", \"ts\": \"2025-09-05 14:52:15.319\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2101_ZSVOP\", \"ts\": \"2025-09-05 14:53:10.805\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1944\", \"ts\": \"2025-09-05 14:52:09.816\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1917\", \"ts\": \"2025-09-05 14:52:26.016\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2317\", \"ts\": \"2025-09-05 14:52:09.92\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2034\", \"ts\": \"2025-09-05 14:52:05.42\", \"value\": \"11\", \"shadow\": \"11\"}, {\"id\": \"SVG_2509\", \"ts\": \"2025-09-05 14:52:18.615\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_GUP3\", \"ts\": \"2025-09-05 14:53:10.799\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2303_FC7E\", \"ts\": \"2025-09-05 14:52:02.403\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1921\", \"ts\": \"2025-09-05 14:52:26.019\", \"value\": \"0.05000000074505806\", \"shadow\": \"0.05000000074505806\"}, {\"id\": \"SVG_1912\", \"ts\": \"2025-09-05 14:52:26.013\", \"value\": \"1.100000023841858\", \"shadow\": \"1.100000023841858\"}, {\"id\": \"2203_CH7\", \"ts\": \"2025-09-05 14:52:02.385\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2101_SOCOA1\", \"ts\": \"2025-09-05 14:53:10.806\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1004\", \"ts\": \"2025-09-05 14:52:29.186\", \"value\": \"15.5\", \"shadow\": \"15.5\"}, {\"id\": \"SVG_2330\", \"ts\": \"2025-09-05 14:52:15.289\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2004\", \"ts\": \"2025-09-05 14:52:09.84\", \"value\": \"4\", \"shadow\": \"4\"}, {\"id\": \"SVG_2001\", \"ts\": \"2025-09-05 14:52:09.838\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1208\", \"ts\": \"2025-09-05 14:52:25.908\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1911\", \"ts\": \"2025-09-05 14:52:26.012\", \"value\": \"0.5\", \"shadow\": \"0.5\"}, {\"id\": \"SVG_1932\", \"ts\": \"2025-09-05 14:52:26.038\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2005\", \"ts\": \"2025-09-05 14:52:09.841\", \"value\": \"5.0\", \"shadow\": \"5.0\"}, {\"id\": \"SVG_1009\", \"ts\": \"2025-09-05 14:52:29.192\", \"value\": \"7.777777671813965\", \"shadow\": \"7.777777671813965\"}, {\"id\": \"2101_GUP2\", \"ts\": \"2025-09-05 14:53:10.798\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2324\", \"ts\": \"2025-09-05 14:52:15.284\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1925\", \"ts\": \"2025-09-05 14:52:18.363\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1209\", \"ts\": \"2025-09-05 14:52:29.22\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2032\", \"ts\": \"2025-09-05 14:52:05.419\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1903\", \"ts\": \"2025-09-05 14:52:18.345\", \"value\": \"200.0\", \"shadow\": \"200.0\"}, {\"id\": \"2101_PUHP\", \"ts\": \"2025-09-05 14:53:10.816\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2401\", \"ts\": \"2025-09-05 14:52:15.294\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2201_CH1\", \"ts\": \"2025-09-05 14:52:02.378\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2301_FC4O\", \"ts\": \"2025-09-05 14:52:05.654\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_2301\", \"ts\": \"2025-09-05 14:52:05.426\", \"value\": \"33686018\", \"shadow\": \"33686018\"}, {\"id\": \"2101_GOP2\", \"ts\": \"2025-09-05 14:53:10.795\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2409\", \"ts\": \"2025-09-05 14:52:15.3\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_RS485CTF\", \"ts\": \"2025-09-05 14:53:10.819\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2309\", \"ts\": \"2025-09-05 14:52:09.913\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1804\", \"ts\": \"2025-09-05 14:52:18.331\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1934\", \"ts\": \"2025-09-05 14:52:26.04\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"2102_CCST\", \"ts\": \"2025-09-05 14:53:10.829\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2101_GVRSP\", \"ts\": \"2025-09-05 14:53:10.802\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_PM\", \"ts\": \"2025-09-05 14:52:15.317\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1945\", \"ts\": \"2025-09-05 14:52:09.817\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1915\", \"ts\": \"2025-09-05 14:52:26.015\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"2102_GLV2P\", \"ts\": \"2025-09-05 14:53:10.841\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2503\", \"ts\": \"2025-09-05 14:52:15.304\", \"value\": \"0.10000000149011612\", \"shadow\": \"0.10000000149011612\"}, {\"id\": \"SVG_2403\", \"ts\": \"2025-09-05 14:52:15.296\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2203_CH5\", \"ts\": \"2025-09-05 14:52:05.645\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1012\", \"value\": \"\", \"shadow\": \"\"}, {\"id\": \"SVG_2007\", \"ts\": \"2025-09-05 14:52:09.842\", \"value\": \"7.0\", \"shadow\": \"7.0\"}, {\"id\": \"SVG_1715\", \"ts\": \"2025-09-05 14:52:18.325\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"1914_FRCE\", \"ts\": \"2025-09-05 14:52:15.323\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2504\", \"ts\": \"2025-09-05 14:52:15.304\", \"value\": \"0.10000000149011612\", \"shadow\": \"0.10000000149011612\"}, {\"id\": \"1914_NSCS\", \"ts\": \"2025-09-05 14:51:56.922\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1908\", \"ts\": \"2025-09-05 14:52:26.01\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1701\", \"ts\": \"2025-09-05 14:52:25.938\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1952\", \"ts\": \"2025-09-05 14:52:09.823\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_UL\", \"ts\": \"2025-09-05 14:52:15.312\", \"value\": \"8\", \"shadow\": \"8\"}, {\"id\": \"SVG_1510\", \"ts\": \"2025-09-05 14:52:25.932\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1502\", \"ts\": \"2025-09-05 14:52:25.927\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2028\", \"ts\": \"2025-09-05 14:52:05.416\", \"value\": \"8.0\", \"shadow\": \"8.0\"}, {\"id\": \"2303_FC2E\", \"ts\": \"2025-09-05 14:52:02.399\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_CCK1ND\", \"ts\": \"2025-09-05 14:53:10.826\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_TSNCSDF\", \"ts\": \"2025-09-05 14:53:10.825\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2002\", \"ts\": \"2025-09-05 14:52:09.838\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_1207\", \"ts\": \"2025-09-05 14:52:25.907\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_WWRP\", \"ts\": \"2025-09-05 14:53:10.823\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2021\", \"ts\": \"2025-09-05 14:52:05.41\", \"value\": \"3.0\", \"shadow\": \"3.0\"}, {\"id\": \"1914_LVRSE\", \"ts\": \"2025-09-05 14:52:15.318\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1306\", \"ts\": \"2025-09-05 14:52:29.227\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1817\", \"ts\": \"2025-09-05 14:52:18.342\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2325\", \"ts\": \"2025-09-05 14:52:15.285\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1014\", \"value\": \"\", \"shadow\": \"\"}, {\"id\": \"2102_ARFCLP\", \"ts\": \"2025-09-05 14:53:10.841\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1923\", \"ts\": \"2025-09-05 14:52:18.361\", \"value\": \"0.0037499999161809683\", \"shadow\": \"0.0037499999161809683\"}, {\"id\": \"2101_PUSIP\", \"ts\": \"2025-09-05 14:53:10.818\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2501\", \"ts\": \"2025-09-05 14:52:15.302\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2402\", \"ts\": \"2025-09-05 14:52:15.295\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1008\", \"ts\": \"2025-09-05 14:52:29.19\", \"value\": \"6.123456954956055\", \"shadow\": \"6.123456954956055\"}, {\"id\": \"1914_LVRT\", \"ts\": \"2025-09-05 14:52:15.323\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_FPLP\", \"ts\": \"2025-09-05 14:53:10.837\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1950\", \"ts\": \"2025-09-05 14:52:09.822\", \"value\": \"0.20000000298023224\", \"shadow\": \"0.20000000298023224\"}, {\"id\": \"SVG_2316\", \"ts\": \"2025-09-05 14:52:09.919\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1802\", \"ts\": \"2025-09-05 14:52:18.33\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1505\", \"ts\": \"2025-09-05 14:52:25.929\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2305\", \"ts\": \"2025-09-05 14:52:05.429\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2410\", \"ts\": \"2025-09-05 14:52:15.301\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1810\", \"ts\": \"2025-09-05 14:52:18.336\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2202_RS\", \"ts\": \"2025-09-05 14:52:05.643\", \"value\": \"14\", \"shadow\": \"14\"}, {\"id\": \"2301_FC1PS\", \"ts\": \"2025-09-05 14:52:05.65\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2101_SZSCFP\", \"ts\": \"2025-09-05 14:53:10.813\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_HSFCF\", \"ts\": \"2025-09-05 14:53:10.847\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_CSM\", \"ts\": \"2025-09-05 14:52:15.318\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2508\", \"ts\": \"2025-09-05 14:52:18.614\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2407\", \"ts\": \"2025-09-05 14:52:15.299\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1964\", \"ts\": \"2025-09-05 14:52:09.836\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_DCSM\", \"ts\": \"2025-09-05 14:51:56.921\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1955\", \"ts\": \"2025-09-05 14:52:09.825\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2003\", \"ts\": \"2025-09-05 14:52:09.839\", \"value\": \"3.0\", \"shadow\": \"3.0\"}, {\"id\": \"SVG_1405\", \"ts\": \"2025-09-05 14:52:29.236\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1709\", \"ts\": \"2025-09-05 14:52:18.32\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1309\", \"ts\": \"2025-09-05 14:52:29.23\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1965\", \"ts\": \"2025-09-05 14:52:09.837\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1961_VCP\", \"ts\": \"2025-09-05 14:51:56.925\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2102_DWPF\", \"ts\": \"2025-09-05 14:53:10.822\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1930\", \"ts\": \"2025-09-05 14:52:18.367\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1308\", \"ts\": \"2025-09-05 14:52:29.229\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1002\", \"ts\": \"2025-09-05 14:52:29.183\", \"value\": \"2.799999952316284\", \"shadow\": \"2.799999952316284\"}, {\"id\": \"2301_FC3O\", \"ts\": \"2025-09-05 14:52:05.652\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_1905\", \"ts\": \"2025-09-05 14:52:18.347\", \"value\": \"50.0\", \"shadow\": \"50.0\"}, {\"id\": \"SVG_2311\", \"ts\": \"2025-09-05 14:52:09.915\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_PUSCFP\", \"ts\": \"2025-09-05 14:53:10.817\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1803\", \"ts\": \"2025-09-05 14:52:18.33\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1307\", \"ts\": \"2025-09-05 14:52:29.228\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2201_CH2\", \"ts\": \"2025-09-05 14:52:02.379\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1707\", \"ts\": \"2025-09-05 14:52:25.943\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_2020\", \"ts\": \"2025-09-05 14:52:05.409\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"1914_NLM\", \"ts\": \"2025-09-05 14:52:15.32\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_WCSCF\", \"ts\": \"2025-09-05 14:53:10.845\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1818\", \"ts\": \"2025-09-05 14:52:18.343\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_CA\", \"ts\": \"2025-09-05 14:51:56.916\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2009\", \"ts\": \"2025-09-05 14:52:09.844\", \"value\": \"8\", \"shadow\": \"8\"}, {\"id\": \"SVG_2304\", \"ts\": \"2025-09-05 14:52:05.429\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1960_RSV\", \"ts\": \"2025-09-05 14:51:56.924\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2201_CH3\", \"ts\": \"2025-09-05 14:52:02.38\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1607\", \"ts\": \"2025-09-05 14:52:25.938\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2202_RM\", \"ts\": \"2025-09-05 14:52:02.381\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1941\", \"ts\": \"2025-09-05 14:52:09.814\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2008\", \"ts\": \"2025-09-05 14:52:09.843\", \"value\": \"9.0\", \"shadow\": \"9.0\"}, {\"id\": \"SVG_1962\", \"ts\": \"2025-09-05 14:52:09.835\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1809\", \"ts\": \"2025-09-05 14:52:18.336\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2033\", \"ts\": \"2025-09-05 14:52:05.419\", \"value\": \"10.0\", \"shadow\": \"10.0\"}, {\"id\": \"SVG_1919\", \"ts\": \"2025-09-05 14:52:26.018\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1927\", \"ts\": \"2025-09-05 14:52:18.364\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2302\", \"ts\": \"2025-09-05 14:52:05.427\", \"value\": \"33686018\", \"shadow\": \"33686018\"}, {\"id\": \"SVG_2026\", \"ts\": \"2025-09-05 14:52:05.414\", \"value\": \"4\", \"shadow\": \"4\"}, {\"id\": \"2101_DRTF\", \"ts\": \"2025-09-05 14:53:10.82\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1203\", \"ts\": \"2025-09-05 14:52:29.209\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1001\", \"ts\": \"2025-09-05 14:52:29.182\", \"value\": \"5.489999771118164\", \"shadow\": \"5.489999771118164\"}, {\"id\": \"2301_FC1O\", \"ts\": \"2025-09-05 14:52:05.649\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_1938\", \"ts\": \"2025-09-05 14:52:26.047\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2027\", \"ts\": \"2025-09-05 14:52:05.415\", \"value\": \"5\", \"shadow\": \"5\"}, {\"id\": \"SVG_1943\", \"ts\": \"2025-09-05 14:52:09.815\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2502\", \"ts\": \"2025-09-05 14:52:15.303\", \"value\": \"0.10000000149011612\", \"shadow\": \"0.10000000149011612\"}, {\"id\": \"SVG_1928\", \"ts\": \"2025-09-05 14:52:18.365\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1210\", \"ts\": \"2025-09-05 14:52:29.221\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1206\", \"ts\": \"2025-09-05 14:52:25.906\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1407\", \"ts\": \"2025-09-05 14:52:29.238\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_WCSA\", \"ts\": \"2025-09-05 14:53:10.845\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_SCM\", \"ts\": \"2025-09-05 14:52:15.314\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1926\", \"ts\": \"2025-09-05 14:52:18.363\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_CCRT\", \"ts\": \"2025-09-05 14:53:10.83\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2023\", \"ts\": \"2025-09-05 14:52:05.412\", \"value\": \"5.0\", \"shadow\": \"5.0\"}, {\"id\": \"1961_LRFDM\", \"ts\": \"2025-09-05 14:51:56.926\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1601\", \"ts\": \"2025-09-05 14:52:25.933\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1202\", \"ts\": \"2025-09-05 14:52:29.208\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2312\", \"ts\": \"2025-09-05 14:52:09.916\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1401\", \"ts\": \"2025-09-05 14:52:29.232\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_TOA\", \"ts\": \"2025-09-05 14:53:10.832\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2306\", \"ts\": \"2025-09-05 14:52:05.43\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1936\", \"ts\": \"2025-09-05 14:52:18.371\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"2303_FC5E\", \"ts\": \"2025-09-05 14:52:02.401\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2012\", \"ts\": \"2025-09-05 14:52:09.846\", \"value\": \"13.0\", \"shadow\": \"13.0\"}, {\"id\": \"SVG_2102\", \"ts\": \"2025-09-05 14:52:05.423\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1304\", \"ts\": \"2025-09-05 14:52:29.225\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1956\", \"ts\": \"2025-09-05 14:52:15.141\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2404\", \"ts\": \"2025-09-05 14:52:15.296\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1939\", \"ts\": \"2025-09-05 14:52:09.812\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1713\", \"ts\": \"2025-09-05 14:52:18.324\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"2303_FC4E\", \"ts\": \"2025-09-05 14:52:02.401\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1906\", \"ts\": \"2025-09-05 14:52:26.008\", \"value\": \"500.0\", \"shadow\": \"500.0\"}, {\"id\": \"SVG_1501\", \"ts\": \"2025-09-05 14:52:25.926\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_VCT\", \"ts\": \"2025-09-05 14:51:56.92\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2101_SOCHOP\", \"ts\": \"2025-09-05 14:53:10.809\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_BCE\", \"ts\": \"2025-09-05 14:52:15.314\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1816\", \"ts\": \"2025-09-05 14:52:18.341\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1717\", \"ts\": \"2025-09-05 14:52:18.327\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1957\", \"ts\": \"2025-09-05 14:52:15.142\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2302_FC7PS\", \"ts\": \"2025-09-05 14:52:02.396\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2301_FC2PS\", \"ts\": \"2025-09-05 14:52:05.652\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2006\", \"ts\": \"2025-09-05 14:52:09.841\", \"value\": \"6\", \"shadow\": \"6\"}, {\"id\": \"SVG_1702\", \"ts\": \"2025-09-05 14:52:25.939\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"2101_GIOP\", \"ts\": \"2025-09-05 14:53:10.797\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_DF\", \"ts\": \"2025-09-05 14:53:10.821\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1409\", \"ts\": \"2025-09-05 14:52:29.24\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2327\", \"ts\": \"2025-09-05 14:52:15.286\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1960\", \"ts\": \"2025-09-05 14:52:05.39\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1404\", \"ts\": \"2025-09-05 14:52:29.235\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2202_RSV\", \"ts\": \"2025-09-05 14:52:05.644\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2024\", \"ts\": \"2025-09-05 14:52:05.413\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_2329\", \"ts\": \"2025-09-05 14:52:15.288\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1918\", \"ts\": \"2025-09-05 14:52:26.017\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1714\", \"ts\": \"2025-09-05 14:52:18.325\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_2405\", \"ts\": \"2025-09-05 14:52:15.297\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2313\", \"ts\": \"2025-09-05 14:52:09.917\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1937\", \"ts\": \"2025-09-05 14:52:18.372\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2018\", \"ts\": \"2025-09-05 14:52:05.408\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2303_RSV\", \"ts\": \"2025-09-05 14:51:57.008\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1922\", \"ts\": \"2025-09-05 14:52:26.02\", \"value\": \"50.0\", \"shadow\": \"50.0\"}, {\"id\": \"2102_ICFBP\", \"ts\": \"2025-09-05 14:53:10.843\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_TPT\", \"ts\": \"2025-09-05 14:53:10.836\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_WCSPF\", \"ts\": \"2025-09-05 14:53:10.831\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1716\", \"ts\": \"2025-09-05 14:52:18.326\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"2302_FC6PS\", \"ts\": \"2025-09-05 14:52:02.395\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1904\", \"ts\": \"2025-09-05 14:52:18.346\", \"value\": \"900.0\", \"shadow\": \"900.0\"}, {\"id\": \"SVG_2029\", \"ts\": \"2025-09-05 14:52:05.416\", \"value\": \"9\", \"shadow\": \"9\"}, {\"id\": \"SVG_1310\", \"ts\": \"2025-09-05 14:52:29.231\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1305\", \"ts\": \"2025-09-05 14:52:29.226\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1916\", \"ts\": \"2025-09-05 14:52:26.016\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2408\", \"ts\": \"2025-09-05 14:52:15.3\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1605\", \"ts\": \"2025-09-05 14:52:25.936\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2314\", \"ts\": \"2025-09-05 14:52:09.917\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1013\", \"value\": \"\", \"shadow\": \"\"}, {\"id\": \"SVG_2505\", \"ts\": \"2025-09-05 14:52:15.305\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1506\", \"ts\": \"2025-09-05 14:52:25.93\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1604\", \"ts\": \"2025-09-05 14:52:25.935\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_HCAM\", \"ts\": \"2025-09-05 14:51:56.917\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2333\", \"ts\": \"2025-09-05 14:52:15.291\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2332\", \"ts\": \"2025-09-05 14:52:15.291\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_USCF\", \"ts\": \"2025-09-05 14:53:10.838\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1805\", \"ts\": \"2025-09-05 14:52:18.332\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1406\", \"ts\": \"2025-09-05 14:52:29.237\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2303_FC8E\", \"ts\": \"2025-09-05 14:52:02.404\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_NLME\", \"ts\": \"2025-09-05 14:52:15.316\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1907\", \"ts\": \"2025-09-05 14:52:26.009\", \"value\": \"2.0\", \"shadow\": \"2.0\"}, {\"id\": \"SVG_1807\", \"ts\": \"2025-09-05 14:52:18.334\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1801\", \"ts\": \"2025-09-05 14:52:18.329\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1408\", \"ts\": \"2025-09-05 14:52:29.239\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_CTF\", \"ts\": \"2025-09-05 14:53:10.824\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2323\", \"ts\": \"2025-09-05 14:52:15.283\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_RCT\", \"ts\": \"2025-09-05 14:52:15.322\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1507\", \"ts\": \"2025-09-05 14:52:25.93\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1909\", \"ts\": \"2025-09-05 14:52:26.01\", \"value\": \"0.0010416667209938169\", \"shadow\": \"0.0010416667209938169\"}, {\"id\": \"SVG_2315\", \"ts\": \"2025-09-05 14:52:09.918\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_BTM\", \"ts\": \"2025-09-05 14:52:15.321\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2302_FC6O\", \"ts\": \"2025-09-05 14:52:02.394\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"2102_RFPE\", \"ts\": \"2025-09-05 14:53:10.831\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1951\", \"ts\": \"2025-09-05 14:52:09.822\", \"value\": \"0.0005000000237487257\", \"shadow\": \"0.0005000000237487257\"}, {\"id\": \"SVG_1946\", \"ts\": \"2025-09-05 14:52:09.817\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2326\", \"ts\": \"2025-09-05 14:52:15.286\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2319\", \"ts\": \"2025-09-05 14:52:09.921\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_PUGF\", \"ts\": \"2025-09-05 14:53:10.814\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1913\", \"ts\": \"2025-09-05 14:52:26.013\", \"value\": \"0.0010416667209938169\", \"shadow\": \"0.0010416667209938169\"}, {\"id\": \"SVG_2328\", \"ts\": \"2025-09-05 14:52:15.287\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1603\", \"ts\": \"2025-09-05 14:52:25.935\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2334\", \"ts\": \"2025-09-05 14:52:15.292\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_PTFP\", \"ts\": \"2025-09-05 14:53:10.812\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_GVFF\", \"ts\": \"2025-09-05 14:52:15.315\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2318\", \"ts\": \"2025-09-05 14:52:09.92\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_TLGA\", \"ts\": \"2025-09-05 14:53:10.834\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1948\", \"ts\": \"2025-09-05 14:52:09.819\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_GOA1\", \"ts\": \"2025-09-05 14:53:10.794\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2310\", \"ts\": \"2025-09-05 14:52:09.914\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1811\", \"ts\": \"2025-09-05 14:52:18.337\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_TPA\", \"ts\": \"2025-09-05 14:53:10.836\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1703\", \"ts\": \"2025-09-05 14:52:25.94\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1910\", \"ts\": \"2025-09-05 14:52:26.011\", \"value\": \"0.5\", \"shadow\": \"0.5\"}, {\"id\": \"2302_FC7O\", \"ts\": \"2025-09-05 14:52:02.395\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"2301_FC3PS\", \"ts\": \"2025-09-05 14:52:05.653\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_CBQF1ND\", \"ts\": \"2025-09-05 14:53:10.828\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1708\", \"ts\": \"2025-09-05 14:52:25.944\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1959\", \"ts\": \"2025-09-05 14:52:05.389\", \"value\": \"0.0020000000949949026\", \"shadow\": \"0.0020000000949949026\"}, {\"id\": \"SVG_1814\", \"ts\": \"2025-09-05 14:52:18.34\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_THGT\", \"ts\": \"2025-09-05 14:53:10.835\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2036\", \"ts\": \"2025-09-05 14:52:05.422\", \"value\": \"13\", \"shadow\": \"13\"}, {\"id\": \"1914_OM\", \"ts\": \"2025-09-05 14:51:56.919\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1402\", \"ts\": \"2025-09-05 14:52:29.233\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1007\", \"ts\": \"2025-09-05 14:52:29.189\", \"value\": \"0.20000000298023224\", \"shadow\": \"0.20000000298023224\"}, {\"id\": \"SVG_1204\", \"ts\": \"2025-09-05 14:52:29.21\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1935\", \"ts\": \"2025-09-05 14:52:18.37\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"2101_SOCOP2\", \"ts\": \"2025-09-05 14:53:10.807\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1933\", \"ts\": \"2025-09-05 14:52:26.039\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_2013\", \"ts\": \"2025-09-05 14:52:09.847\", \"value\": \"12\", \"shadow\": \"12\"}, {\"id\": \"2102_WCSOSA\", \"ts\": \"2025-09-05 14:53:10.844\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1602\", \"ts\": \"2025-09-05 14:52:25.934\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2322\", \"ts\": \"2025-09-05 14:52:15.283\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1929\", \"ts\": \"2025-09-05 14:52:18.366\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2331\", \"ts\": \"2025-09-05 14:52:15.29\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_CCK1NE\", \"ts\": \"2025-09-05 14:53:10.826\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1961\", \"ts\": \"2025-09-05 14:52:09.834\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2030\", \"ts\": \"2025-09-05 14:52:05.417\", \"value\": \"3\", \"shadow\": \"3\"}, {\"id\": \"SVG_1015\", \"value\": \"\", \"shadow\": \"\"}, {\"id\": \"2203_CH8\", \"ts\": \"2025-09-05 14:52:05.649\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"2303_FC1E\", \"ts\": \"2025-09-05 14:52:02.398\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2507\", \"ts\": \"2025-09-05 14:52:15.307\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1812\", \"ts\": \"2025-09-05 14:52:18.338\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1011\", \"value\": \"\", \"shadow\": \"\"}, {\"id\": \"2102_CBQF1NE\", \"ts\": \"2025-09-05 14:53:10.827\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1949\", \"ts\": \"2025-09-05 14:52:05.381\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1710\", \"ts\": \"2025-09-05 14:52:18.321\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_2025\", \"ts\": \"2025-09-05 14:52:05.413\", \"value\": \"7\", \"shadow\": \"7\"}, {\"id\": \"1914_RPC\", \"ts\": \"2025-09-05 14:52:18.617\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2303_FC3E\", \"ts\": \"2025-09-05 14:52:02.4\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2406\", \"ts\": \"2025-09-05 14:52:15.298\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2017\", \"ts\": \"2025-09-05 14:52:05.407\", \"value\": \"16\", \"shadow\": \"16\"}, {\"id\": \"SVG_1924\", \"ts\": \"2025-09-05 14:52:18.362\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1901\", \"ts\": \"2025-09-05 14:52:18.344\", \"value\": \"150.0\", \"shadow\": \"150.0\"}, {\"id\": \"SVG_1914\", \"ts\": \"2025-09-05 14:52:26.014\", \"value\": \"805584928\", \"shadow\": \"805584928\"}, {\"id\": \"SVG_1201\", \"ts\": \"2025-09-05 14:52:29.207\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2019\", \"ts\": \"2025-09-05 14:52:05.409\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1410\", \"ts\": \"2025-09-05 14:52:29.241\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2506\", \"ts\": \"2025-09-05 14:52:15.306\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_CM\", \"ts\": \"2025-09-05 14:52:18.617\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2302_FC8PS\", \"ts\": \"2025-09-05 14:52:02.397\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1960_RPOE\", \"ts\": \"2025-09-05 14:51:56.923\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1606\", \"ts\": \"2025-09-05 14:52:25.937\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2301_FC2O\", \"ts\": \"2025-09-05 14:52:05.651\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_1806\", \"ts\": \"2025-09-05 14:52:18.333\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2308\", \"ts\": \"2025-09-05 14:52:09.912\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2202\", \"ts\": \"2025-09-05 14:52:05.425\", \"value\": \"28\", \"shadow\": \"28\"}, {\"id\": \"SVG_2010\", \"ts\": \"2025-09-05 14:52:09.845\", \"value\": \"11.0\", \"shadow\": \"11.0\"}, {\"id\": \"SVG_1403\", \"ts\": \"2025-09-05 14:52:29.234\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2303_FC6E\", \"ts\": \"2025-09-05 14:52:02.402\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1705\", \"ts\": \"2025-09-05 14:52:25.942\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1301\", \"ts\": \"2025-09-05 14:52:29.222\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1503\", \"ts\": \"2025-09-05 14:52:25.927\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_WCSRS\", \"ts\": \"2025-09-05 14:53:10.846\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1947\", \"ts\": \"2025-09-05 14:52:09.818\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2302_FC8O\", \"ts\": \"2025-09-05 14:52:02.397\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_1718\", \"ts\": \"2025-09-05 14:52:18.328\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1953\", \"ts\": \"2025-09-05 14:52:09.824\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_PUUIP\", \"ts\": \"2025-09-05 14:53:10.815\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1960_VPOE\", \"ts\": \"2025-09-05 14:51:56.924\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1711\", \"ts\": \"2025-09-05 14:52:18.322\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"1914_PN\", \"ts\": \"2025-09-05 14:52:15.313\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2201_CH4\", \"ts\": \"2025-09-05 14:52:02.38\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2307\", \"ts\": \"2025-09-05 14:52:05.431\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1205\", \"ts\": \"2025-09-05 14:52:29.211\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1902\", \"ts\": \"2025-09-05 14:52:18.345\", \"value\": \"380.0\", \"shadow\": \"380.0\"}, {\"id\": \"SVG_2015\", \"ts\": \"2025-09-05 14:52:09.849\", \"value\": \"14\", \"shadow\": \"14\"}, {\"id\": \"SVG_1931\", \"ts\": \"2025-09-05 14:52:18.367\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"1914_BCVC\", \"ts\": \"2025-09-05 14:51:56.918\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2101_GOP3\", \"ts\": \"2025-09-05 14:53:10.796\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_1815\", \"ts\": \"2025-09-05 14:52:18.341\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2203\", \"ts\": \"2025-09-05 14:52:05.426\", \"value\": \"16843009\", \"shadow\": \"16843009\"}, {\"id\": \"SVG_2320\", \"ts\": \"2025-09-05 14:52:09.922\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2102_FRTP\", \"ts\": \"2025-09-05 14:53:10.842\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1704\", \"ts\": \"2025-09-05 14:52:25.941\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"2101_SSLP\", \"ts\": \"2025-09-05 14:53:10.803\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_ARSM\", \"ts\": \"2025-09-05 14:51:56.919\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1813\", \"ts\": \"2025-09-05 14:52:18.339\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1508\", \"ts\": \"2025-09-05 14:52:25.931\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1003\", \"ts\": \"2025-09-05 14:52:29.185\", \"value\": \"8.100000381469727\", \"shadow\": \"8.100000381469727\"}, {\"id\": \"SVG_2510\", \"ts\": \"2025-09-05 14:52:18.616\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2031\", \"ts\": \"2025-09-05 14:52:05.418\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"2101_SHSFP\", \"ts\": \"2025-09-05 14:53:10.804\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1920\", \"ts\": \"2025-09-05 14:52:26.019\", \"value\": \"0.05000000074505806\", \"shadow\": \"0.05000000074505806\"}, {\"id\": \"2102_GLV1P\", \"ts\": \"2025-09-05 14:53:10.84\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1504\", \"ts\": \"2025-09-05 14:52:25.928\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_SOCCLA\", \"ts\": \"2025-09-05 14:53:10.81\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1958\", \"ts\": \"2025-09-05 14:52:05.388\", \"value\": \"0.009999999776482582\", \"shadow\": \"0.009999999776482582\"}, {\"id\": \"SVG_1808\", \"ts\": \"2025-09-05 14:52:18.335\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_1006\", \"ts\": \"2025-09-05 14:52:29.188\", \"value\": \"15.0\", \"shadow\": \"15.0\"}, {\"id\": \"SVG_2011\", \"ts\": \"2025-09-05 14:52:09.846\", \"value\": \"10\", \"shadow\": \"10\"}, {\"id\": \"2101_SOCIOP\", \"ts\": \"2025-09-05 14:53:10.808\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2014\", \"ts\": \"2025-09-05 14:52:09.848\", \"value\": \"15.0\", \"shadow\": \"15.0\"}, {\"id\": \"2101_SICOCDFP\", \"ts\": \"2025-09-05 14:53:10.811\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_TOT\", \"ts\": \"2025-09-05 14:53:10.833\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1954\", \"ts\": \"2025-09-05 14:52:09.825\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_GVPLP\", \"ts\": \"2025-09-05 14:53:10.801\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2101\", \"ts\": \"2025-09-05 14:52:05.423\", \"value\": \"7\", \"shadow\": \"7\"}, {\"id\": \"2203_CH6\", \"ts\": \"2025-09-05 14:52:05.646\", \"value\": \"1\", \"shadow\": \"1\"}, {\"id\": \"SVG_2321\", \"ts\": \"2025-09-05 14:52:15.282\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2035\", \"ts\": \"2025-09-05 14:52:05.421\", \"value\": \"12.0\", \"shadow\": \"12.0\"}, {\"id\": \"2101_RS485CCF\", \"ts\": \"2025-09-05 14:53:10.82\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2301_FC4PS\", \"ts\": \"2025-09-05 14:52:05.655\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2302_FC5PS\", \"ts\": \"2025-09-05 14:52:02.393\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"2102_IOC2T\", \"ts\": \"2025-09-05 14:53:10.839\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1005\", \"ts\": \"2025-09-05 14:52:29.187\", \"value\": \"14.0\", \"shadow\": \"14.0\"}, {\"id\": \"2101_GVIP\", \"ts\": \"2025-09-05 14:53:10.8\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1963\", \"ts\": \"2025-09-05 14:52:09.835\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2101_PUUOP\", \"ts\": \"2025-09-05 14:53:10.815\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_1712\", \"ts\": \"2025-09-05 14:52:18.323\", \"value\": \"6.0\", \"shadow\": \"6.0\"}, {\"id\": \"SVG_1942\", \"ts\": \"2025-09-05 14:52:09.814\", \"value\": \"1.0\", \"shadow\": \"1.0\"}, {\"id\": \"SVG_1302\", \"ts\": \"2025-09-05 14:52:29.223\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"2302_FC5O\", \"ts\": \"2025-09-05 14:52:05.656\", \"value\": \"2\", \"shadow\": \"2\"}, {\"id\": \"SVG_2335\", \"ts\": \"2025-09-05 14:52:15.293\", \"value\": \"0.0\", \"shadow\": \"0.0\"}, {\"id\": \"SVG_2303\", \"ts\": \"2025-09-05 14:52:05.428\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1914_TM\", \"ts\": \"2025-09-05 14:52:15.324\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"1961_RSV\", \"ts\": \"2025-09-05 14:51:56.927\", \"value\": \"0\", \"shadow\": \"0\"}, {\"id\": \"SVG_2022\", \"ts\": \"2025-09-05 14:52:05.411\", \"value\": \"4\", \"shadow\": \"4\"}]",
    "activeTime": "2025-08-27",
    "createTime": null,
    "gwDevCode": null,
    "locationWay": 1,
    "imgUrl": "",
    "isOwner": null,
    "subDeviceCount": null,
    "slaveId": null,
    "transport": null,
    "protocolCode": null,
    "deptName": null,
    "createBy": null,
    "canSeeCode": null,
    "alertCount": null,
    "guid": null,
    "thingsModels": [
      {
        "id": "2202_RSV",
        "name": "(备用) 故障录波设置",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20409,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_CSM",
        "name": "(CTO 采样点模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20381,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC6E",
        "name": "(滤波通道 6 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20435,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1938",
        "name": "（SVG变压器变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20247,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2305",
        "name": "（滤波通道2输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20320,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2012",
        "name": "（线电压幅值Ⅲ段欠压保护值）SVG保护参数",
        "value": "13.0",
        "shadow": "13.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20286,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1951",
        "name": "（锁相环Ki）系统参数",
        "value": "0.0005000000237487257",
        "shadow": "0.0005000000237487257",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20260,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1716",
        "name": "（通道16校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20189,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC6O",
        "name": "(滤波通道 6 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20424,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_ZSVOP",
        "name": "(零序电压超标保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20061,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1209",
        "name": "（积分初始值）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20135,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1906",
        "name": "（单元载波频率）系统参数",
        "value": "500.0",
        "shadow": "500.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20215,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2004",
        "name": "（线电压有效值Ⅱ段过压保护时间）SVG保护参数",
        "value": "4",
        "shadow": "4",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20278,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1943",
        "name": "（PWM板2级数）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20252,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2317",
        "name": "（滤波通道6指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20332,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_NLME",
        "name": "(空载模式使能) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20378,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SOCOA1",
        "name": "(SVG 输出电流有效值 Ⅰ 段过流报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20062,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUUOP",
        "name": "(功率单元 UDC 过压保护 (SW) 使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20072,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1003",
        "name": "（积分系数）直流电压",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20114,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1011",
        "name": "（比例系数）直接电流",
        "value": "",
        "shadow": "",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20122
      },
      {
        "id": "1914_BCE",
        "name": "(平衡控制使能) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20376,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUSIP",
        "name": "(功率单元状态不一致保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20076,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1501",
        "name": "（比例系数）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20157,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CBQF1ND",
        "name": "(断路器 QF1 不分开使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20088,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2203_CH6",
        "name": "(CH6 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20411,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_FRCE",
        "name": "(快速无功补偿使能) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20387,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC4PS",
        "name": "(滤波通道 4 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20421,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1919",
        "name": "（电网一段电压变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20228,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GOA1",
        "name": "(电网线电压有效值 Ⅰ 段过压报警使能) 保护功能使能",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20048,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1205",
        "name": "（输出上限）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20131,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC1O",
        "name": "(滤波通道 1 次数) 谐波控制参数",
        "value": "23",
        "shadow": "23",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20414,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2101",
        "name": "保护功能使能1",
        "value": "5",
        "shadow": "5",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20311,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2016",
        "name": "（线电压缺相保护值）SVG保护参数",
        "value": "17.0",
        "shadow": "17.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20290,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_HSFCF",
        "name": "(高速光纤通信故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20111,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_RS485CTF",
        "name": "(RS485 通信超时故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20077,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_USCF",
        "name": "(单元短路故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20100,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2201_CH1",
        "name": "(CH1 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20403,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2333",
        "name": "（H6通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20348,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2505",
        "name": "（输出上限）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20365,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_SCM",
        "name": "(SVG 连接方式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20375,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1942",
        "name": "（PWM板1级数）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20251,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1928",
        "name": "（低电压穿越保护阀值1）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20237,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2328",
        "name": "（H1通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20343,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1802",
        "name": "（通道2偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20193,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2033",
        "name": "（输出电流(CT采样)瞬时值过流跳闸保护值）SVG保护参数",
        "value": "10.0",
        "shadow": "10.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20307,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2011",
        "name": "（线电压有效值Ⅱ段欠压保护时间）SVG保护参数",
        "value": "10",
        "shadow": "10",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20285,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2025",
        "name": "（输出电流缺相保护时间）SVG保护参数",
        "value": "7",
        "shadow": "7",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20299,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC5O",
        "name": "(滤波通道 5 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20422,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1605",
        "name": "（断路器超时控制）充电控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20171,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2303",
        "name": "滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20318,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2509",
        "name": "（积分初始值）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20369,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1914",
        "name": "控制模式1",
        "value": "839254706",
        "shadow": "839254706",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20223,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1010",
        "name": "（备用）直流电压",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20121,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1702",
        "name": "（通道2校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20175,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1932",
        "name": "（功率因数设定值）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20241,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC3O",
        "name": "(滤波通道 3 次数) 谐波控制参数",
        "value": "5",
        "shadow": "5",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20418,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2510",
        "name": "（备用1）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20370,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GUP2",
        "name": "(电网线电压有效值 Ⅱ 段欠压保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20053,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1510",
        "name": "（备用）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20166,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1507",
        "name": "（积分上限）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20163,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_DWPF",
        "name": "(DRAM 写参数故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20081,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1005",
        "name": "（输出上限）直流电压",
        "value": "14.0",
        "shadow": "14.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20116,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1607",
        "name": "（旁路接触器动作时间）充电控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20173,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1709",
        "name": "（通道9校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20182,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TLGA",
        "name": "(变压器轻瓦斯报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20095,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1931",
        "name": "（PT板RC移相角度补偿）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20240,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2203_CH8",
        "name": "(CH8 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20413,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1712",
        "name": "（通道12校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20185,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2026",
        "name": "（单元电压过压保护值）SVG保护参数",
        "value": "4",
        "shadow": "4",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20300,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1710",
        "name": "（通道10校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20183,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2401",
        "name": "（调节比例系数）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20351,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_ARSM",
        "name": "(AVC 无功设定模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20393,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1704",
        "name": "（通道4校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20177,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2010",
        "name": "（线电压有效值Ⅱ段欠压保护值）SVG保护参数",
        "value": "11.0",
        "shadow": "11.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20284,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2408",
        "name": "（积分下限）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20358,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TOT",
        "name": "(变压器超温跳闸使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20094,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUSCFP",
        "name": "(功率单元自检故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20075,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GUP3",
        "name": "(电网线电压幅值 Ⅲ 段欠压保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20054,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2029",
        "name": "（输出电流瞬时值过流跳闸时间）SVG保护参数",
        "value": "9",
        "shadow": "9",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20303,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1502",
        "name": "（kc系数）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20158,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1912",
        "name": "（输出电流上限）系统参数",
        "value": "2.059999942779541",
        "shadow": "2.059999942779541",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20221,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2409",
        "name": "（积分初始值）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20359,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1606",
        "name": "（断路器合超时控制）充电控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20172,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1924",
        "name": "（感性无功偏移系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20233,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC7O",
        "name": "(滤波通道 7 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20426,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1954",
        "name": "（传输延时补偿角度）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20263,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC8O",
        "name": "(滤波通道 8 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20428,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2007",
        "name": "（线电压瞬时值过压保护值）SVG保护参数",
        "value": "7.0",
        "shadow": "7.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20281,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_TM",
        "name": "(测试模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20388,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_BCVC",
        "name": "(平衡控制电压指令) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20391,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2009",
        "name": "（线电压有效值Ⅰ段欠压报警时间）SVG保护参数",
        "value": "8",
        "shadow": "8",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20283,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC7PS",
        "name": "(滤波通道 7 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20427,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1965",
        "name": "（q轴负向辅助校正系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20274,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2404",
        "name": "（调节微分系数）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20354,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1404",
        "name": "（微分系数）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20150,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_LVRSE",
        "name": "(低电压无功支撑使能) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20380,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CCRT",
        "name": "(CAN 通信接收超时使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20090,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2302",
        "name": "谐波控制参数",
        "value": "33686018",
        "shadow": "33686018",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20317,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2330",
        "name": "（H3通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20345,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1958",
        "name": "（超前滞后环节Tb系数）系统参数",
        "value": "0.009999999776482582",
        "shadow": "0.009999999776482582",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20267,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_FPLP",
        "name": "(风机缺相保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20099,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2312",
        "name": "（滤波通道1指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20327,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2405",
        "name": "（输出上限）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20355,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1308",
        "name": "（积分下限）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20144,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2331",
        "name": "（H4通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20346,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_RFPE",
        "name": "(读铁电参数错误使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20092,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_RPC",
        "name": "(无功电流给定补偿) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20371,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WCSA",
        "name": "(水冷系统报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20109,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_ARFCLP",
        "name": "(自动恢复失败次数超限保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20104,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2314",
        "name": "（滤波通道3指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20329,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1907",
        "name": "（连接电感）系统参数",
        "value": "2.0",
        "shadow": "2.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20216,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1403",
        "name": "（积分系数）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20149,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC2E",
        "name": "(滤波通道 2 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20431,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC2O",
        "name": "(滤波通道 2 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20416,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_MSM",
        "name": "(主从机模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20382,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1949",
        "name": "（PWM板6级数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20258,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1815",
        "name": "（通道15偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20206,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2310",
        "name": "（滤波通道7输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20325,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2316",
        "name": "（滤波通道5指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20331,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2315",
        "name": "（滤波通道4指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20330,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2036",
        "name": "（零序电流过流保护时间）SVG保护参数",
        "value": "13",
        "shadow": "13",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20310,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1603",
        "name": "（充电时间下限）充电控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20169,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1936",
        "name": "（电网电压指令）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20245,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1902",
        "name": "（额定电压）系统参数",
        "value": "380.0",
        "shadow": "380.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20211,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1504",
        "name": "（微分系数）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20160,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1202",
        "name": "（kc系数）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20128,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1506",
        "name": "（输出下限）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20162,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_ABSSCFP",
        "name": "(模拟板采样通道自检故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20060,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1952",
        "name": "（SVG升压变压器移相角度）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20261,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2008",
        "name": "（线电压有效值Ⅰ段欠压报警值）SVG保护参数",
        "value": "9.0",
        "shadow": "9.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20282,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1961",
        "name": "控制模式3",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20270,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1930",
        "name": "（电网二段电压变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20239,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1809",
        "name": "（通道9偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20200,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1961_LRFDM",
        "name": "(负载无功快速检测模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20401,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1905",
        "name": "（电网频率）系统参数",
        "value": "200.0",
        "shadow": "200.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20214,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_BTM",
        "name": "(母联模式) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20384,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC1E",
        "name": "(滤波通道 1 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20430,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2030",
        "name": "（连续故障次数）SVG保护参数",
        "value": "3",
        "shadow": "3",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20304,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CCST",
        "name": "(CAN 通信发送超时使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20089,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_VCT",
        "name": "(电压补偿目标点) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20394,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1204",
        "name": "（微分系数）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20130,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WCSPF",
        "name": "(水冷系统电源故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20091,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TSNCSDF",
        "name": "(行程开关未合故障使能 / 烟感故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20084,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1503",
        "name": "（积分系数）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20159,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2203_CH7",
        "name": "(CH7 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20412,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1925",
        "name": "（感性无功增益）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20234,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1960_RPOE",
        "name": "(无功补偿并联运行使能) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20397,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GIOP",
        "name": "(电网线电压瞬时值过压保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20051,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2324",
        "name": "（H5通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20339,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1805",
        "name": "（通道5偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20196,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2308",
        "name": "（滤波通道5输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20323,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2503",
        "name": "（调节积分系数）电网电压无功控制",
        "value": "0.10000000149011612",
        "shadow": "0.10000000149011612",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20363,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2031",
        "name": "（故障复位超时时间）SVG保护参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20305,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CBQF1NE",
        "name": "(断路器 QF1 不吸合使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20087,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2014",
        "name": "（线电压幅度不平衡度保护值）SVG保护参数",
        "value": "15.0",
        "shadow": "15.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20288,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1602",
        "name": "（充电时间上限）充电控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20168,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_PM",
        "name": "(锁相环模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20379,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2024",
        "name": "（输出电流缺相保护值）SVG保护参数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20298,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1911",
        "name": "（无功指令电流）系统参数",
        "value": "999.0",
        "shadow": "999.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20220,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_PN",
        "name": "(相数) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20374,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2410",
        "name": "（备用1）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20360,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2019",
        "name": "（输出电流有效值Ⅰ段过流报警值）SVG保护参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20293,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2003",
        "name": "（线电压有效值Ⅱ段过压保护值）SVG保护参数",
        "value": "3.0",
        "shadow": "3.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20277,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1946",
        "name": "（输出电压q轴无功电流系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20255,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PTFP",
        "name": "(PT 故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20069,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_RSV",
        "name": "(预留) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20438,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1961_VCP",
        "name": "(电压补偿点) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20400,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1817",
        "name": "（通道17偏移系数）采样偏移量b",
        "value": "16.0",
        "shadow": "16.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20208,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2018",
        "name": "（故障重新启动等待时间）SVG保护参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20292,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1714",
        "name": "（通道14校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20187,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SOCCLA",
        "name": "(SVG 输出电流指令限幅报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20067,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GUA1",
        "name": "(电网线电压有效值 Ⅰ 段欠压报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20052,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1940",
        "name": "（单元过压保护跳闸时间）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20249,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_IOC2T",
        "name": "(瞬时过流 2 跳闸使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20101,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SOCOP2",
        "name": "(SVG 输出电流有效值 Ⅱ 段过流保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20063,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1937",
        "name": "（电网电压指令斜坡）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20246,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2334",
        "name": "（H7通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20349,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_DF",
        "name": "(DRAM 故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20080,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2023",
        "name": "（输出电流瞬时值过流封脉冲保护值）SVG保护参数",
        "value": "5.0",
        "shadow": "5.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20297,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1915",
        "name": "（电网一段电流CT变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20224,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1941",
        "name": "（单元过压时间）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20250,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_FRTP",
        "name": "(故障复位超时保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20105,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1703",
        "name": "（通道3校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20176,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1933",
        "name": "（上级网侧电流1段变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20242,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2322",
        "name": "（H3通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20337,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1006",
        "name": "（输出下限）直流电压",
        "value": "15.0",
        "shadow": "15.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20117,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1955",
        "name": "（平衡控制补偿角度）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20264,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1206",
        "name": "（输出下限）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20132,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUHP",
        "name": "(功率单元硬件保护 (HW) 使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20074,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TPA",
        "name": "(变压器压力报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20097,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2319",
        "name": "（滤波通道8指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20334,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1921",
        "name": "（直流侧与无功电流系数）系统参数",
        "value": "0.05000000074505806",
        "shadow": "0.05000000074505806",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20230,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1947",
        "name": "（PWM板4级数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20256,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC1PS",
        "name": "(滤波通道 1 相序) 谐波控制参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20415,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1922",
        "name": "（无功电流滞后系数）系统参数",
        "value": "50.0",
        "shadow": "50.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20231,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC7E",
        "name": "(滤波通道 7 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20436,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2201_CH4",
        "name": "(CH4 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20406,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1406",
        "name": "（输出下限）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20152,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2502",
        "name": "（调节Kc系数）电网电压无功控制",
        "value": "0.10000000149011612",
        "shadow": "0.10000000149011612",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20362,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2021",
        "name": "（输出电流有效值Ⅱ段过流保护值）SVG保护参数",
        "value": "3.0",
        "shadow": "3.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20295,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1409",
        "name": "（积分初始值）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20155,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GVPLP",
        "name": "(电网线电压缺相保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20056,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_GVFF",
        "name": "(电网电压前馈滤波) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20377,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_CM",
        "name": "(控制模式) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20372,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1306",
        "name": "（输出下限）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20142,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1007",
        "name": "（积分上限）直流电压",
        "value": "0.20000000298023224",
        "shadow": "0.20000000298023224",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20118,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_2320",
        "name": "（H1通道Kp系数）谐波控制参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20335,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1505",
        "name": "（输出上限）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20161,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WCSCF",
        "name": "(水冷系统综合故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20108,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1916",
        "name": "（SVG霍尔电流传感器变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20225,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2306",
        "name": "（滤波通道3输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20321,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2311",
        "name": "（滤波通道8输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20326,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1715",
        "name": "（通道15校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20188,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GVIP",
        "name": "(电网线电压有效值不平衡保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20055,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2017",
        "name": "（线电压缺相保护时间）SVG保护参数",
        "value": "16",
        "shadow": "16",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20291,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1801",
        "name": "（通道1偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20192,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2318",
        "name": "（滤波通道7指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20333,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1816",
        "name": "（通道16偏移系数）采样偏移量A",
        "value": "51.0",
        "shadow": "51.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20207,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1807",
        "name": "（通道7偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20198,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC6PS",
        "name": "(滤波通道 6 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20425,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2002",
        "name": "（线电压有效值Ⅰ段过压报警时间）SVG保护参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20276,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1713",
        "name": "（通道13校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20186,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2005",
        "name": "（线电压幅值Ⅲ段过压保护值）SVG保护参数",
        "value": "5.0",
        "shadow": "5.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20279,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1004",
        "name": "（微分系数）直流电压",
        "value": "15.5",
        "shadow": "15.5",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20115,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1935",
        "name": "（电压基准）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20244,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2508",
        "name": "（积分下限）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20368,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TOA",
        "name": "(变压器超温报警使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20093,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1962",
        "name": "（d轴正向辅助校正系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20271,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUUIP",
        "name": "(功率单元 UDC 不平衡保护 (SW) 使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20073,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SSLP",
        "name": "(同步信号丢失保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20058,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2201_CH2",
        "name": "(CH2 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20404,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1939",
        "name": "（电压补偿死区）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20248,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1960_TVOVS",
        "name": "（并联运行电压目标值同步）控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20439,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC8PS",
        "name": "(滤波通道 8 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20429,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2202",
        "name": "故障录波设置",
        "value": "28",
        "shadow": "28",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20314,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1810",
        "name": "（通道10偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20201,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1408",
        "name": "（积分下限）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20154,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1009",
        "name": "（积分初始值）直流电压",
        "value": "7.777777671813965",
        "shadow": "7.777777671813965",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20120,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_2013",
        "name": "（线电压幅值Ⅲ段欠压保护时间）SVG保护参数",
        "value": "12",
        "shadow": "12",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20287,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1945",
        "name": "（输出电压d轴无功电流系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20254,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2201_CH3",
        "name": "(CH3 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20405,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1706",
        "name": "（通道6校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20179,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2020",
        "name": "（输出电流有效值Ⅰ段过流报警时间）SVG保护参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20294,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2202_RM",
        "name": "(录波模式) 故障录波设置",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20407,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1803",
        "name": "（通道3偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20194,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2307",
        "name": "（滤波通道4输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20322,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_NLM",
        "name": "(空载模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20383,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2321",
        "name": "（H2通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20336,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2327",
        "name": "（H8通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20342,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2309",
        "name": "（滤波通道6输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20324,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1948",
        "name": "（PWM板5级数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20257,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2501",
        "name": "（调节比例系数）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20361,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1405",
        "name": "（输出上限）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20151,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1808",
        "name": "（通道8偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20199,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1909",
        "name": "（斜坡速率）系统参数",
        "value": "0.0010416667209938169",
        "shadow": "0.0010416667209938169",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20218,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1410",
        "name": "（备用）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20156,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1013",
        "name": "（输出上限）直接电流",
        "value": "",
        "shadow": "",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20124
      },
      {
        "id": "SVG_2201",
        "name": "故障录波参数",
        "value": "16843009",
        "shadow": "16843009",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20313,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1012",
        "name": "（积分系数）直接电流",
        "value": "",
        "shadow": "",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20123
      },
      {
        "id": "SVG_1203",
        "name": "（积分系数）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20129,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1207",
        "name": "（积分上限）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20133,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WCSOSA",
        "name": "(水冷系统运行状态异常使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20107,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2402",
        "name": "（调节Kc系数）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20352,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1806",
        "name": "（通道6偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20197,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC3PS",
        "name": "(滤波通道 3 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20419,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_RCT",
        "name": "(无功补偿目标点) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20385,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2203_CH5",
        "name": "(CH5 通道录波变量) 故障录波参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20410,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1811",
        "name": "（通道11偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20202,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1508",
        "name": "（积分下限）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20164,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1960_VPOE",
        "name": "(电压补偿并联运行使能) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20398,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1917",
        "name": "（电网二段电流CT变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20226,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1711",
        "name": "（通道11校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20184,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1707",
        "name": "（通道7校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20180,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1953",
        "name": "（模拟板移相角）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20262,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1304",
        "name": "（微分系数）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20140,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2406",
        "name": "（输出下限）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20356,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC3E",
        "name": "(滤波通道 3 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20432,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1963",
        "name": "（q轴正向辅助校正系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20272,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1305",
        "name": "（输出上限）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20141,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1904",
        "name": "（直流侧额定电压）系统参数",
        "value": "900.0",
        "shadow": "900.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20213,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GOP3",
        "name": "(电网线电压幅值 Ⅲ 段过压保护使能) 保护功能使能",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20050,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2325",
        "name": "（H6通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20340,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GVRSP",
        "name": "(电网电压反序保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20057,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2022",
        "name": "（输出电流有效值Ⅱ段过流保护时间）SVG保护参数",
        "value": "4",
        "shadow": "4",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20296,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1705",
        "name": "（通道5校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20178,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1927",
        "name": "（容性无功增益）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20236,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2504",
        "name": "（调节微分系数）电网电压无功控制",
        "value": "0.10000000149011612",
        "shadow": "0.10000000149011612",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20364,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_THGT",
        "name": "(变压器重瓦斯跳闸使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20096,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1401",
        "name": "（比例系数）相直流侧电压平衡控制",
        "value": "2.009999990463257",
        "shadow": "2.009999990463257",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20147,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2035",
        "name": "（零序电流瞬时值过流保护定值）SVG保护参数",
        "value": "12.0",
        "shadow": "12.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20309,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_HCAM",
        "name": "(谐波补偿手_自动运行) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20390,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2302_FC5PS",
        "name": "(滤波通道 5 相序) 谐波控制参数",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20423,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1814",
        "name": "（通道14偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20205,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WCSRS",
        "name": "(水冷系统请求停止使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20110,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC4E",
        "name": "(滤波通道 4 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20433,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1002",
        "name": "（kc系数）直流电压",
        "value": "2.799999952316284",
        "shadow": "2.799999952316284",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20113,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "1914_NSCS",
        "name": "(负序电流抑制模式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20396,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2203",
        "name": "故障录波参数",
        "value": "16843009",
        "shadow": "16843009",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20315,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2323",
        "name": "（H4通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20338,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2506",
        "name": "（输出下限）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20366,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2403",
        "name": "（调节积分系数）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20353,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CCK1ND",
        "name": "(充电接触器 K1 不分开使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20086,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1718",
        "name": "（通道18校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20191,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2027",
        "name": "（单元电压不平衡保护值）SVG保护参数",
        "value": "5",
        "shadow": "5",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20301,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1913",
        "name": "（无功指令电流斜坡）系统参数",
        "value": "0.0010416667209938169",
        "shadow": "0.0010416667209938169",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20222,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1957",
        "name": "（直流电流抑制Ki）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20266,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2032",
        "name": "（霍尔传感器故障保护定值）SVG保护参数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20306,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_LVRT",
        "name": "(低电压穿越) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20386,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1302",
        "name": "（kc系数）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20138,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2507",
        "name": "（积分上限）电网电压无功控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20367,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1812",
        "name": "（通道12偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20203,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2407",
        "name": "（积分上限）电网电压控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20357,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1015",
        "name": "（备用）直接电流",
        "value": "",
        "shadow": "",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20126
      },
      {
        "id": "SVG_1309",
        "name": "（积分初始值）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20145,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2304",
        "name": "（滤波通道1输出电流上限）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20319,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1903",
        "name": "（电流定标系数）系统参数",
        "value": "200.0",
        "shadow": "200.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20212,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2028",
        "name": "（输出电流瞬时值过流跳闸保护值）SVG保护参数",
        "value": "8.0",
        "shadow": "8.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20302,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_ICFBP",
        "name": "(柜间光纤断保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20106,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1303",
        "name": "（积分系数）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20139,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1920",
        "name": "（无功电流下限）系统参数",
        "value": "0.05000000074505806",
        "shadow": "0.05000000074505806",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20229,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1210",
        "name": "（备用）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20136,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1818",
        "name": "（通道18偏移系数）采样偏移量C",
        "value": "18.0",
        "shadow": "18.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20209,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1813",
        "name": "（通道13偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20204,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1961_RSV",
        "name": "(备用) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20402,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC5E",
        "name": "(滤波通道 5 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20434,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_PUGF",
        "name": "(功率单元状态一般性故障 (SW) 使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20071,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CCK1NE",
        "name": "(充电接触器 K1 不吸合使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20085,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_GLV2P",
        "name": "(电网低电压 2 保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20103,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1708",
        "name": "（通道8校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20181,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SOCHOP",
        "name": "(SVG 输出电流硬件过流 (HW) 保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20065,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_CA",
        "name": "(柜体排列方式) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20389,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC2PS",
        "name": "(滤波通道 2 相序) 谐波控制参数",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20417,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2332",
        "name": "（H5通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20347,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2329",
        "name": "（H2通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20344,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1964",
        "name": "（d轴负向辅助校正系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20273,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2326",
        "name": "（H7通道Kp系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20341,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1601",
        "name": "（预充电电压）充电控制",
        "value": "10.0",
        "shadow": "10.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20167,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SICOCDFP",
        "name": "(SVG 瞬时电流过流（CT 检测方式）故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20068,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2301",
        "name": "谐波控制参数",
        "value": "33915543",
        "shadow": "33915543",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20316,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2102",
        "name": "保护功能使能2",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20312,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1208",
        "name": "（积分下限）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20134,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_WWRP",
        "name": "(WDI 看门狗复位保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20082,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1923",
        "name": "（死区校正系数）系统参数",
        "value": "0.0037499999161809683",
        "shadow": "0.0037499999161809683",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20232,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_GOP2",
        "name": "(电网线电压有效值 Ⅱ 段过压保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20049,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_DRTF",
        "name": "(DRAM 读超时故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20079,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1717",
        "name": "（通道17校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20190,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2202_RS",
        "name": "(录波状态) 故障录波设置",
        "value": "14",
        "shadow": "14",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20408,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2335",
        "name": "（H8通道Ki系数）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20350,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1001",
        "name": "（比例系数）直流电压",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20112,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1407",
        "name": "（积分上限）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20153,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SOCIOP",
        "name": "(SVG 输出电流瞬时值过流保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20064,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2034",
        "name": "（输出电流(CT采样)瞬时值过流跳闸时间）SVG保护参数",
        "value": "11",
        "shadow": "11",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20308,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1908",
        "name": "（直流侧电压指令值）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20217,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_UL",
        "name": "(单元级数) 控制模式",
        "value": "12",
        "shadow": "12",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20373,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1604",
        "name": "（接触器超时控制）充电控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20170,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1926",
        "name": "（容性无功偏移系数）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20235,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1959",
        "name": "（超前滞后环节Ta系数）系统参数",
        "value": "0.0020000000949949026",
        "shadow": "0.0020000000949949026",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20268,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1934",
        "name": "（上级网侧电流2段变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20243,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1918",
        "name": "（SVG保护CT变比）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20227,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_GLV1P",
        "name": "(电网低电压 1 保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20102,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1307",
        "name": "（积分上限）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20143,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1301",
        "name": "（比例系数）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20137,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1310",
        "name": "（备用）间接电流无功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20146,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1960_RSV",
        "name": "(备用) 控制模式",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20399,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1929",
        "name": "（低电压穿越保护阀值2）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20238,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1402",
        "name": "（kc系数）相直流侧电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20148,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2313",
        "name": "（滤波通道2指令电流）谐波控制参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20328,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_DCSM",
        "name": "(直流电流抑制模式) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20395,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1944",
        "name": "（PWM板3级数）系统参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20253,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1201",
        "name": "（比例系数）间接电流有功",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20127,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_CTF",
        "name": "(充电超时故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20083,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1950",
        "name": "（锁相环Kp）系统参数",
        "value": "0.20000000298023224",
        "shadow": "0.20000000298023224",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20259,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2006",
        "name": "（线电压幅值Ⅲ段过压保护时间）SVG保护参数",
        "value": "6",
        "shadow": "6",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20280,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2102_TPT",
        "name": "(变压器压力跳闸使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20098,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1956",
        "name": "（直流电流抑制Kp）系统参数",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20265,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2303_FC8E",
        "name": "(滤波通道 8 使能) 滤波使能控制",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20437,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SZSCFP",
        "name": "(SVG 零序电流故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20070,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2001",
        "name": "（线电压有效值Ⅰ段过压报警值）SVG保护参数",
        "value": "1.0",
        "shadow": "1.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20275,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1960",
        "name": "控制模式2",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20269,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1008",
        "name": "（积分下限）直流电压",
        "value": "6.123456954956055",
        "shadow": "6.123456954956055",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20119,
        "ts": "2025-09-24 14:21:10"
      },
      {
        "id": "SVG_1509",
        "name": "（积分初始值）单元电压平衡控制",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20165,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1804",
        "name": "（通道4偏移系数）采样偏移量",
        "value": "0.0",
        "shadow": "0.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20195,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1701",
        "name": "（通道1校正系数）采样校正系数",
        "value": "6.0",
        "shadow": "6.0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20174,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1014",
        "name": "（积分上限）直接电流",
        "value": "",
        "shadow": "",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20125
      },
      {
        "id": "2101_SOCPLP",
        "name": "(SVG 输出电流缺相保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20066,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_2015",
        "name": "（线电压幅度不平衡保护时间）SVG保护参数",
        "value": "14",
        "shadow": "14",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20289,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "1914_OM",
        "name": "(运行模式) 控制模式",
        "value": "1",
        "shadow": "1",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20392,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_SHSFP",
        "name": "(SVG 侧霍尔传感器故障保护使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20059,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2101_RS485CCF",
        "name": "(RS485 通信校验故障使能) 保护功能使能",
        "value": "0",
        "shadow": "0",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20078,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "2301_FC4O",
        "name": "(滤波通道 4 次数) 谐波控制参数",
        "value": "2",
        "shadow": "2",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "integer",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20420,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1910",
        "name": "（斜坡输出最小值）系统参数",
        "value": "0.5",
        "shadow": "0.5",
        "isMonitor": 1,
        "isHistory": 1,
        "isChart": 1,
        "isReadonly": 1,
        "isApp": 0,
        "order": 0,
        "type": 1,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20219,
        "ts": "2025-09-24 14:21:11"
      },
      {
        "id": "SVG_1901",
        "name": "（额定电流kvar）系统参数",
        "value": "22.0",
        "shadow": "22.0",
        "isMonitor": 0,
        "isHistory": 1,
        "isChart": 0,
        "isReadonly": 0,
        "isApp": 0,
        "order": 0,
        "type": 2,
        "datatype": {
          "type": "decimal",
          "falseText": "",
          "trueText": "",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "",
          "arrayType": "",
          "arrayCount": 0,
          "arrayIndex": null,
          "showWay": null,
          "maxLength": 1024,
          "enumList": null,
          "params": null,
          "arrayParams": null,
          "parentType": null,
          "parentName": null,
          "parentIdentifier": null,
          "parentIndexName": null
        },
        "isSharePerm": 0,
        "modelId": 20210,
        "ts": "2025-09-24 14:21:11"
      }
    ],
    "stringList": [],
    "integerList": [],
    "decimalList": [],
    "enumList": [],
    "arrayList": [],
    "boolList": [],
    "readOnlyList": []
  }
}
```

## MQTT协议
服务器地址： 'ws://192.168.0.205:8083/mqtt'
实现的脚本： ./webgl/mqttTool.js

### SVG电气拓扑
订阅主题: /189/D19QBHKRZ791U/ws/service

